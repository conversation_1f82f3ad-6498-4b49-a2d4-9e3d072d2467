module.exports = {
  // 测试环境
  testEnvironment: 'node',
  
  // 测试文件匹配模式
  testMatch: [
    '**/test/**/*.test.js',
    '**/test/**/*.spec.js'
  ],
  
  // 覆盖率配置
  collectCoverage: false, // 默认不收集，通过命令行参数控制
  collectCoverageFrom: [
    'lib/**/*.js',
    '!lib/**/*.test.js',
    '!lib/**/*.spec.js'
  ],
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov'
  ],
  
  // 覆盖率输出目录
  coverageDirectory: 'coverage',
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // 设置超时时间
  testTimeout: 10000,
  
  // 清理模拟
  clearMocks: true,
  restoreMocks: true,
  
  // 详细输出
  verbose: true,
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  
  // 模块路径映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/lib/$1'
  },
  
  // 忽略的路径
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/'
  ],
  
  // 转换忽略模式
  transformIgnorePatterns: [
    '/node_modules/'
  ]
};
