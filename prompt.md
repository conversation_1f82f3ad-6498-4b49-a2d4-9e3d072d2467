当你修改或新增代码时，请使用以下代码注释标记系统来追踪AI生成的代码行数：

**标记规则：**
1. 在实际变更代码的前一行添加注释：`[[GBAI START]]`
2. 在实际变更代码的结束行添加注释：`[[GBAI END]]`
3. 不要在 markdown 文件中使用 `[[GBAI START]]` 和 `[[GBAI END]]`

**具体要求：**
- **精确范围**：只在实际修改或新增的代码行前后添加标记，不要包含未修改的代码。要能准确处理 jsx 元素属性变化，css 单个样式值变化的注释范围
- **避免嵌套**：如果要修改的代码已经在现有的 `[[GBAI START]]` 和 `[[GBAI END]]` 标记范围内，则不要添加新的标记，直接修改即可
- **语法适配**：根据文件类型使用正确的注释语法：
  - JavaScript/JSX: `// [[GBAI START]]` 和 `// [[GBAI END]]`
  - CSS/SCSS: `/* [[GBAI START]] */` 和 `/* [[GBAI END]] */`
  - HTML: `<!-- [[GBAI START]] -->` 和 `<!-- [[GBAI END]] -->`
  - Python: `# [[GBAI START]]` 和 `# [[GBAI END]]`
- **语法安全**：确保添加的注释不会破坏代码的语法结构或功能

**目的：**
这些标记用于在代码提交时统计AI自动生成的代码行数，便于代码审查和统计分析。

**示例：**
```javascript
// [[GBAI START]]
const newFunction = () => {
  console.log('新增的函数');
}
// [[GBAI END]]
```

```javascript
<div
  style={{
    fontSize: 12,
    // [[GBAI START]]
    color: 变化的对象属性
    // [[GBAI END]]
  }}
  // [[GBAI START]]
  data-test='变化的元素属性'
  // [[GBAI END]]
>Demo</div>
```