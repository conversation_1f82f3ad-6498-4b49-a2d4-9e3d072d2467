/**
 * 测试模拟工具集合
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 模拟文件系统操作
 */
class MockFileSystem {
  constructor() {
    this.files = new Map();
    this.directories = new Set();
  }

  // 设置文件内容
  setFile(filePath, content) {
    this.files.set(filePath, content);
    // 自动创建目录
    const dir = path.dirname(filePath);
    this.directories.add(dir);
  }

  // 获取文件内容
  getFile(filePath) {
    return this.files.get(filePath);
  }

  // 检查文件是否存在
  exists(filePath) {
    return this.files.has(filePath) || this.directories.has(filePath);
  }

  // 删除文件
  deleteFile(filePath) {
    this.files.delete(filePath);
  }

  // 清空所有文件
  clear() {
    this.files.clear();
    this.directories.clear();
  }

  // 模拟fs.readFileSync
  mockReadFileSync() {
    return jest.spyOn(fs, 'readFileSync').mockImplementation((filePath, encoding) => {
      const content = this.getFile(filePath);
      if (content === undefined) {
        const error = new Error(`ENOENT: no such file or directory, open '${filePath}'`);
        error.code = 'ENOENT';
        throw error;
      }
      return content;
    });
  }

  // 模拟fs.writeFileSync
  mockWriteFileSync() {
    return jest.spyOn(fs, 'writeFileSync').mockImplementation((filePath, content) => {
      this.setFile(filePath, content);
    });
  }

  // 模拟fs.existsSync
  mockExistsSync() {
    return jest.spyOn(fs, 'existsSync').mockImplementation((filePath) => {
      return this.exists(filePath);
    });
  }

  // 模拟fs.statSync
  mockStatSync() {
    return jest.spyOn(fs, 'statSync').mockImplementation((filePath) => {
      if (!this.exists(filePath)) {
        const error = new Error(`ENOENT: no such file or directory, stat '${filePath}'`);
        error.code = 'ENOENT';
        throw error;
      }
      
      return {
        isFile: () => this.files.has(filePath),
        isDirectory: () => this.directories.has(filePath)
      };
    });
  }

  // 模拟fs.chmodSync
  mockChmodSync() {
    return jest.spyOn(fs, 'chmodSync').mockImplementation(() => {
      // 模拟成功执行
    });
  }

  // 模拟fs.mkdirSync
  mockMkdirSync() {
    return jest.spyOn(fs, 'mkdirSync').mockImplementation((dirPath) => {
      this.directories.add(dirPath);
    });
  }

  // 一次性设置所有fs模拟
  mockAll() {
    return {
      readFileSync: this.mockReadFileSync(),
      writeFileSync: this.mockWriteFileSync(),
      existsSync: this.mockExistsSync(),
      statSync: this.mockStatSync(),
      chmodSync: this.mockChmodSync(),
      mkdirSync: this.mockMkdirSync()
    };
  }
}

/**
 * 模拟Git命令执行
 */
class MockGitCommands {
  constructor() {
    this.commands = new Map();
    this.defaultResponses = {
      'git rev-parse --show-toplevel': '/mock/repo/path',
      'git rev-parse HEAD': 'abc123def456',
      'git log --format="%H|%an|%ae|%s|%ai" -1': 'abc123def456|Test Author|<EMAIL>|Test commit message|2023-12-01 10:00:00 +0000',
      'git diff --numstat': '10\t5\tfile1.js\n20\t0\tfile2.js',
      'git diff-tree --no-commit-id --name-only -r': 'file1.js\nfile2.js',
      'git show': 'mock file content',
      'git diff --cached --name-only': 'staged-file.js',
      'git diff --cached --quiet': ''
    };
  }

  // 设置特定命令的响应
  setCommandResponse(command, response, shouldThrow = false) {
    this.commands.set(command, { response, shouldThrow });
  }

  // 获取命令响应
  getCommandResponse(command) {
    // 检查精确匹配
    if (this.commands.has(command)) {
      return this.commands.get(command);
    }

    // 检查模式匹配
    for (const [pattern, response] of this.commands.entries()) {
      if (command.includes(pattern) || command.match(new RegExp(pattern))) {
        return response;
      }
    }

    // 检查默认响应
    for (const [pattern, response] of Object.entries(this.defaultResponses)) {
      if (command.includes(pattern) || command.startsWith(pattern)) {
        return { response, shouldThrow: false };
      }
    }

    // 默认响应
    return { response: '', shouldThrow: false };
  }

  // 模拟execSync
  mockExecSync() {
    return jest.spyOn(require('child_process'), 'execSync').mockImplementation((command, options) => {
      const { response, shouldThrow } = this.getCommandResponse(command);
      
      if (shouldThrow) {
        const error = new Error(response);
        error.status = 1;
        throw error;
      }
      
      return response;
    });
  }

  // 清空所有命令响应
  clear() {
    this.commands.clear();
  }
}

/**
 * 创建测试用的AI代码内容
 */
function createAICodeContent(normalLines = 5, aiLines = 3) {
  const lines = [];
  
  // 添加普通代码行
  for (let i = 0; i < normalLines; i++) {
    lines.push(`// Normal code line ${i + 1}`);
    lines.push(`console.log('Normal line ${i + 1}');`);
  }
  
  // 添加AI代码块
  if (aiLines > 0) {
    lines.push('// [[GBAI START]]');
    for (let i = 0; i < aiLines; i++) {
      lines.push(`// AI generated code line ${i + 1}`);
      lines.push(`console.log('AI line ${i + 1}');`);
    }
    lines.push('// [[GBAI END]]');
  }
  
  return lines.join('\n');
}

/**
 * 创建测试用的配置对象
 */
function createTestConfig(overrides = {}) {
  return {
    enabled: true,
    autoCleanup: true,
    verbose: true,
    hooks: {
      preCommit: true,
      prePush: false
    },
    api: {
      enabled: false,
      url: 'https://test-api.com/stats',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    },
    ignore: {
      files: ['package.json', 'package-lock.json'],
      patterns: ['*.log', '*.tmp', 'node_modules/**']
    },
    ...overrides
  };
}

module.exports = {
  MockFileSystem,
  MockGitCommands,
  createAICodeContent,
  createTestConfig
};
