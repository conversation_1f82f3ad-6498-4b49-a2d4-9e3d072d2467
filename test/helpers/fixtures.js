/**
 * 测试数据固定装置
 */

/**
 * 示例提交信息
 */
const sampleCommitInfo = {
  hash: 'abc123def456789',
  author: 'Test Author',
  email: '<EMAIL>',
  message: 'Add new feature with AI assistance',
  date: '2023-12-01T10:00:00.000Z'
};

/**
 * 示例仓库信息
 */
const sampleRepoInfo = {
  name: 'test-repo',
  url: 'https://github.com/test/test-repo.git',
  branch: 'main',
  path: '/mock/repo/path'
};

/**
 * 示例代码统计信息
 */
const sampleCodeStats = {
  files: 3,
  totalAdded: 150,
  totalDeleted: 20,
  netChange: 130,
  details: [
    { file: 'src/main.js', added: 50, deleted: 10 },
    { file: 'src/utils.js', added: 80, deleted: 5 },
    { file: 'src/config.js', added: 20, deleted: 5 }
  ],
  ignoredFiles: [
    { file: 'package.json', added: 5, deleted: 2 }
  ]
};

/**
 * 示例AI代码统计信息
 */
const sampleAICodeStats = {
  filesWithAICode: 2,
  totalAILines: 45,
  details: [
    { file: 'src/main.js', aiLines: 25 },
    { file: 'src/utils.js', aiLines: 20 }
  ]
};

/**
 * 包含AI标记的示例文件内容
 */
const fileWithAIMarkers = `// Regular code
function normalFunction() {
  console.log('This is normal code');
}

// [[GBAI START]]
// AI generated function
function aiGeneratedFunction() {
  console.log('This is AI generated code');
  return 'AI result';
}
// [[GBAI END]]

// More regular code
const config = {
  version: '1.0.0'
};

// [[GBAI START]]
// Another AI block
const aiHelper = {
  process: (data) => {
    return data.map(item => item.value);
  }
};
// [[GBAI END]]

module.exports = { normalFunction, aiGeneratedFunction, config, aiHelper };`;

/**
 * 不包含AI标记的示例文件内容
 */
const fileWithoutAIMarkers = `// Regular code only
function normalFunction() {
  console.log('This is normal code');
}

const config = {
  version: '1.0.0'
};

module.exports = { normalFunction, config };`;

/**
 * 示例Git diff输出
 */
const sampleGitDiffOutput = `10	5	src/main.js
20	0	src/utils.js
5	2	package.json
0	10	src/old-file.js`;

/**
 * 示例Git log输出
 */
const sampleGitLogOutput = 'abc123def456|Test Author|<EMAIL>|Add new feature with AI assistance|2023-12-01 10:00:00 +0000';

/**
 * 示例文件列表
 */
const sampleFileList = [
  'src/main.js',
  'src/utils.js',
  'src/config.js',
  'test/main.test.js',
  'package.json',
  'README.md'
];

/**
 * 示例staged文件列表
 */
const sampleStagedFiles = [
  'src/main.js',
  'src/utils.js'
];

/**
 * 示例API响应
 */
const sampleAPIResponse = {
  success: true,
  message: 'Stats received successfully',
  id: 'stats-123'
};

/**
 * 示例错误响应
 */
const sampleErrorResponse = {
  success: false,
  message: 'Invalid data format',
  error: 'VALIDATION_ERROR'
};

/**
 * 示例package.json内容
 */
const samplePackageJson = {
  name: 'test-project',
  version: '1.0.0',
  scripts: {
    test: 'jest'
  },
  devDependencies: {
    jest: '^29.0.0'
  }
};

/**
 * 示例Git钩子内容
 */
const samplePrePushHook = `#!/bin/bash
# AI代码统计钩子 - 在push时触发

echo "Running AI code stats..."
node /path/to/ai-code-stats.js
`;

/**
 * 示例Husky钩子内容
 */
const sampleHuskyPreCommitHook = `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# AI代码统计和清理 - Pre-commit Hook
echo "🔍 正在统计AI代码并清理标记..."
node "/path/to/ai-code-stats.js" --mode=pre-commit --detailed
`;

/**
 * 创建测试用的完整统计数据
 */
function createCompleteStatsData(overrides = {}) {
  return {
    timestamp: new Date().toISOString(),
    repo: sampleRepoInfo,
    commit: sampleCommitInfo,
    codeStats: sampleCodeStats,
    aiCodeStats: sampleAICodeStats,
    ...overrides
  };
}

/**
 * 创建测试用的文件内容映射
 */
function createFileContentMap() {
  return new Map([
    ['src/main.js', fileWithAIMarkers],
    ['src/utils.js', fileWithAIMarkers],
    ['src/config.js', fileWithoutAIMarkers],
    ['test/main.test.js', fileWithoutAIMarkers],
    ['package.json', JSON.stringify(samplePackageJson, null, 2)],
    ['README.md', '# Test Project\n\nThis is a test project.']
  ]);
}

module.exports = {
  sampleCommitInfo,
  sampleRepoInfo,
  sampleCodeStats,
  sampleAICodeStats,
  fileWithAIMarkers,
  fileWithoutAIMarkers,
  sampleGitDiffOutput,
  sampleGitLogOutput,
  sampleFileList,
  sampleStagedFiles,
  sampleAPIResponse,
  sampleErrorResponse,
  samplePackageJson,
  samplePrePushHook,
  sampleHuskyPreCommitHook,
  createCompleteStatsData,
  createFileContentMap
};
