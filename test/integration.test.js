/**
 * 集成测试 - 测试完整的工作流程
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { MockFileSystem, MockGitCommands, createTestConfig } = require('./helpers/mocks');
const { 
  fileWithAIMarkers, 
  fileWithoutAIMarkers,
  createCompleteStatsData,
  createFileContentMap
} = require('./helpers/fixtures');

// 模拟依赖
jest.mock('fs');
jest.mock('child_process');
jest.mock('https');
jest.mock('http');

describe('集成测试', () => {
  let mockFS;
  let mockGit;
  let originalProcessArgv;
  let originalProcessCwd;

  beforeEach(() => {
    // 保存原始值
    originalProcessArgv = process.argv;
    originalProcessCwd = process.cwd;

    // 初始化模拟对象
    mockFS = new MockFileSystem();
    mockGit = new MockGitCommands();

    // 设置模拟
    mockFS.mockAll();
    mockGit.mockExecSync();

    // 模拟process.cwd
    process.cwd = jest.fn().mockReturnValue('/mock/repo/path');

    // 设置基本的Git响应
    mockGit.setCommandResponse('git rev-parse --show-toplevel', '/mock/repo/path');
    mockGit.setCommandResponse('git config --get remote.origin.url', '************************:test/repo.git');
    mockGit.setCommandResponse('git rev-parse --abbrev-ref HEAD', 'main');

    // 清理所有模块缓存
    Object.keys(require.cache).forEach(key => {
      if (key.includes('lib/')) {
        delete require.cache[key];
      }
    });
  });

  afterEach(() => {
    // 恢复原始值
    process.argv = originalProcessArgv;
    process.cwd = originalProcessCwd;

    // 清理模拟
    jest.clearAllMocks();
    mockGit.clear();
    mockFS.clear();
  });

  describe('完整的AI代码统计流程', () => {
    test('应该完成从统计到清理的完整流程', async () => {
      // 设置：创建测试文件
      const fileMap = createFileContentMap();
      fileMap.forEach((content, filePath) => {
        mockFS.setFile(filePath, content);
      });

      // 设置配置文件
      const config = createTestConfig({ autoCleanup: true, verbose: true });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(config));

      // 设置Git命令响应
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${commitHash}`, 'Test Author <<EMAIL>>');
      mockGit.setCommandResponse(`git show -s --format="%B" ${commitHash}`, 'Test commit message');
      mockGit.setCommandResponse(`git show -s --format="%at" ${commitHash}`, '1701432000');
      
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, 
        '10\t5\tsrc/main.js\n20\t0\tsrc/utils.js\n5\t2\tpackage.json');
      
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 
        'src/main.js\nsrc/utils.js\npackage.json');
      
      mockGit.setCommandResponse(`git show ${commitHash}:src/main.js`, fileWithAIMarkers);
      mockGit.setCommandResponse(`git show ${commitHash}:src/utils.js`, fileWithAIMarkers);

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash, '--detailed'];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该执行完整的统计流程
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');
      expect(execSync).toHaveBeenCalledWith('git config --get remote.origin.url', { encoding: 'utf-8' });
      expect(execSync).toHaveBeenCalledWith(`git diff --numstat ${commitHash}^..${commitHash}`, { encoding: 'utf-8' });
      expect(execSync).toHaveBeenCalledWith(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该在pre-commit模式下正确工作', () => {
      // 设置：创建staged文件
      mockFS.setFile('src/staged.js', fileWithAIMarkers);

      // 设置Git命令响应
      mockGit.setCommandResponse('git diff --cached --quiet', '', true); // 有staged文件
      mockGit.setCommandResponse('git diff --cached --name-only', 'src/staged.js');
      mockGit.setCommandResponse('git show :src/staged.js', fileWithAIMarkers);

      // 设置配置文件
      const config = createTestConfig({ autoCleanup: true });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(config));

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', '--mode=pre-commit', '--detailed'];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行pre-commit模式
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理staged文件
      expect(execSync).toHaveBeenCalledWith('git diff --cached --quiet', expect.any(Object));

      consoleSpy.mockRestore();
    });
  });

  describe('钩子安装集成测试', () => {
    test('应该成功安装Git钩子', () => {
      // 设置：Git仓库环境
      mockFS.directories.add('/mock/repo/path/.git');
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：安装Git钩子
      const installHooks = require('../lib/install-hooks.js');

      // 验证：应该创建钩子文件
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/repo/path/.git/hooks/pre-push',
        expect.stringContaining('#!/bin/bash'),
        undefined
      );
      expect(fs.chmodSync).toHaveBeenCalledWith('/mock/repo/path/.git/hooks/pre-push', '755');

      consoleSpy.mockRestore();
    });

    test('应该成功安装Husky钩子', () => {
      // 设置：package.json文件
      const packageJson = {
        name: 'test-project',
        version: '1.0.0',
        scripts: { test: 'jest' }
      };
      mockFS.setFile('/mock/repo/path/package.json', JSON.stringify(packageJson));
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：安装Husky钩子
      const installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 验证：应该创建Husky钩子文件
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/repo/path/.husky/pre-commit',
        expect.stringContaining('#!/usr/bin/env sh'),
        undefined
      );
      expect(fs.chmodSync).toHaveBeenCalledWith('/mock/repo/path/.husky/pre-commit', '755');

      // 验证：应该更新package.json
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/repo/path/package.json',
        expect.stringContaining('"prepare"'),
        'utf-8'
      );

      consoleSpy.mockRestore();
    });
  });

  describe('清理标记集成测试', () => {
    test('应该成功清理AI标记', () => {
      // 设置：创建包含AI标记的文件
      mockFS.setFile('./src/test1.js', fileWithAIMarkers);
      mockFS.setFile('./src/test2.js', fileWithoutAIMarkers);
      mockFS.setFile('./src/test3.js', fileWithAIMarkers);

      // 设置find命令响应
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', 
        './src/test1.js\n./src/test2.js\n./src/test3.js');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理所有文件
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/test1.js', 'utf-8');
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/test2.js', 'utf-8');
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/test3.js', 'utf-8');

      // 验证：应该写入清理后的文件
      expect(fs.writeFileSync).toHaveBeenCalledWith('./src/test1.js', expect.any(String), 'utf-8');
      expect(fs.writeFileSync).toHaveBeenCalledWith('./src/test3.js', expect.any(String), 'utf-8');
      expect(fs.writeFileSync).not.toHaveBeenCalledWith('./src/test2.js', expect.any(String), 'utf-8');

      consoleSpy.mockRestore();
    });
  });

  describe('配置文件集成测试', () => {
    test('应该正确应用用户配置', () => {
      // 设置：用户配置文件
      const userConfig = createTestConfig({
        verbose: false,
        autoCleanup: false,
        api: {
          enabled: true,
          url: 'https://custom-api.com/stats'
        },
        ignore: {
          files: ['custom-ignore.json'],
          patterns: ['*.custom']
        }
      });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(userConfig));

      // 设置基本的Git响应
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${commitHash}`, 'Test Author <<EMAIL>>');
      mockGit.setCommandResponse(`git show -s --format="%B" ${commitHash}`, 'Test commit');
      mockGit.setCommandResponse(`git show -s --format="%at" ${commitHash}`, '1701432000');
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, '');

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该读取配置文件
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');

      consoleSpy.mockRestore();
    });
  });

  describe('错误处理集成测试', () => {
    test('应该优雅地处理Git命令失败', () => {
      // 设置：Git命令失败
      mockGit.setCommandResponse('git config --get remote.origin.url', 'Command failed', true);

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js'];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试运行AI代码统计
      expect(() => {
        const aiCodeStats = require('../lib/ai-code-stats.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });

    test('应该处理文件系统错误', () => {
      // 设置：文件读取失败
      jest.spyOn(fs, 'readFileSync').mockImplementation((filePath) => {
        if (filePath.includes('.ai-code-stats.json')) {
          throw new Error('Permission denied');
        }
        return mockFS.getFile(filePath);
      });

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js'];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该使用默认配置
      expect(consoleSpy).toHaveBeenCalledWith('⚠️  配置文件格式错误，使用默认配置');

      consoleSpy.mockRestore();
    });
  });
});
