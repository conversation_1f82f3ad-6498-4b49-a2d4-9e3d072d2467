/**
 * install-hooks.js 模块测试
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { MockFileSystem, MockGitCommands } = require('./helpers/mocks');

// 模拟依赖
jest.mock('fs');
jest.mock('path');
jest.mock('child_process');

describe('install-hooks.js', () => {
  let mockFS;
  let mockGit;
  let originalProcessArgv;
  let installHooks;

  beforeEach(() => {
    // 保存原始值
    originalProcessArgv = process.argv;

    // 初始化模拟对象
    mockFS = new MockFileSystem();
    mockGit = new MockGitCommands();

    // 设置模拟
    mockFS.mockAll();
    mockGit.mockExecSync();

    // 模拟path.resolve
    jest.spyOn(path, 'resolve').mockImplementation((dir, file) => {
      return `/mock/absolute/path/${file}`;
    });

    // 模拟path.join
    jest.spyOn(path, 'join').mockImplementation((...parts) => {
      return parts.join('/');
    });

    // 设置Git根目录响应
    mockGit.setCommandResponse('git rev-parse --show-toplevel', '/mock/repo/path');

    // 清理模块缓存
    delete require.cache[require.resolve('../lib/install-hooks.js')];
  });

  afterEach(() => {
    // 恢复原始值
    process.argv = originalProcessArgv;

    // 清理模拟
    jest.clearAllMocks();
    mockGit.clear();
    mockFS.clear();
  });

  describe('基本钩子安装功能', () => {
    test('应该创建hooks目录如果不存在', () => {
      // 设置：hooks目录不存在
      mockFS.setFile('/mock/repo/path/.git/hooks', undefined);

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 验证：应该检查hooks目录是否存在
      expect(fs.existsSync).toHaveBeenCalledWith('/mock/repo/path/.git/hooks');

      // 验证：应该创建hooks目录
      expect(fs.mkdirSync).toHaveBeenCalledWith('/mock/repo/path/.git/hooks', { recursive: true });

      consoleSpy.mockRestore();
    });

    test('应该创建pre-push钩子文件', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 验证：应该写入pre-push钩子文件
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/repo/path/.git/hooks/pre-push',
        expect.stringContaining('#!/bin/bash'),
        undefined
      );

      // 验证：应该设置文件权限
      expect(fs.chmodSync).toHaveBeenCalledWith('/mock/repo/path/.git/hooks/pre-push', '755');

      consoleSpy.mockRestore();
    });

    test('应该在钩子中包含正确的脚本路径', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 验证：钩子内容应该包含正确的脚本路径
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.git/hooks/pre-push'
      )[1];

      expect(hookContent).toContain('/mock/absolute/path/ai-code-stats.js');
      expect(hookContent).toContain('/mock/absolute/path/cleanup-markers.js');

      consoleSpy.mockRestore();
    });

    test('应该输出安装成功消息', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 验证：应该输出成功消息
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Git pre-push钩子已安装到')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('错误处理', () => {
    test('应该处理Git命令失败', () => {
      // 设置：Git命令失败
      mockGit.setCommandResponse('git rev-parse --show-toplevel', 'Not a git repository', true);

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试加载模块
      expect(() => {
        installHooks = require('../lib/install-hooks.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });

    test('应该处理文件写入错误', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟文件写入失败
      jest.spyOn(fs, 'writeFileSync').mockImplementation((filePath) => {
        if (filePath.includes('pre-push')) {
          throw new Error('Permission denied');
        }
      });

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试加载模块
      expect(() => {
        installHooks = require('../lib/install-hooks.js');
      }).toThrow('Permission denied');

      consoleSpy.mockRestore();
    });

    test('应该处理权限设置错误', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟权限设置失败
      jest.spyOn(fs, 'chmodSync').mockImplementation(() => {
        throw new Error('chmod failed');
      });

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试加载模块
      expect(() => {
        installHooks = require('../lib/install-hooks.js');
      }).toThrow('chmod failed');

      consoleSpy.mockRestore();
    });
  });

  describe('钩子内容验证', () => {
    test('钩子应该包含正确的bash shebang', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.git/hooks/pre-push'
      )[1];

      // 验证：应该以正确的shebang开头
      expect(hookContent).toMatch(/^#!/);
      expect(hookContent).toContain('#!/bin/bash');

      consoleSpy.mockRestore();
    });

    test('钩子应该包含日志记录功能', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.git/hooks/pre-push'
      )[1];

      // 验证：应该包含日志功能
      expect(hookContent).toContain('LOG_FILE');
      expect(hookContent).toContain('log_message');

      consoleSpy.mockRestore();
    });

    test('钩子应该包含AI代码统计逻辑', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.git/hooks/pre-push'
      )[1];

      // 验证：应该包含AI代码统计相关逻辑
      expect(hookContent).toContain('ai-code-stats.js');
      expect(hookContent).toContain('AI代码统计');

      consoleSpy.mockRestore();
    });

    test('钩子应该包含清理功能', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.git/hooks/pre-push'
      )[1];

      // 验证：应该包含清理相关逻辑
      expect(hookContent).toContain('cleanup-markers.js');
      expect(hookContent).toContain('清理');

      consoleSpy.mockRestore();
    });
  });

  describe('路径解析', () => {
    test('应该正确解析脚本的绝对路径', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 验证：应该调用path.resolve来获取绝对路径
      expect(path.resolve).toHaveBeenCalledWith(expect.any(String), 'ai-code-stats.js');
      expect(path.resolve).toHaveBeenCalledWith(expect.any(String), 'cleanup-markers.js');

      consoleSpy.mockRestore();
    });

    test('应该正确构建hooks目录路径', () => {
      // 设置：hooks目录存在
      mockFS.directories.add('/mock/repo/path/.git/hooks');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHooks = require('../lib/install-hooks.js');

      // 验证：应该调用path.join来构建路径
      expect(path.join).toHaveBeenCalledWith('/mock/repo/path', '.git', 'hooks');

      consoleSpy.mockRestore();
    });
  });
});
