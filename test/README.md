# AI Code Stats 测试套件

这是 AI Code Stats 项目的完整测试套件，提供全面的单元测试、集成测试和边界情况测试。

## 测试结构

```
test/
├── README.md                 # 测试说明文档
├── setup.js                  # Jest 测试环境设置
├── helpers/                  # 测试辅助工具
│   ├── mocks.js             # 模拟对象和工具函数
│   └── fixtures.js          # 测试数据固定装置
├── ai-code-stats.test.js    # 主要统计模块测试
├── cleanup-markers.test.js  # AI标记清理模块测试
├── install-hooks.test.js    # Git钩子安装模块测试
├── install-husky-hooks.test.js # Husky钩子安装模块测试
├── integration.test.js      # 集成测试
└── edge-cases.test.js       # 边界情况和错误处理测试
```

## 测试覆盖范围

### 单元测试
- **ai-code-stats.js**: 配置加载、仓库信息获取、提交信息获取、代码统计、AI代码统计
- **cleanup-markers.js**: 文件扫描、标记识别、标记清理、错误处理
- **install-hooks.js**: Git钩子文件创建、权限设置、路径解析
- **install-husky-hooks.js**: Husky集成、钩子配置、package.json更新

### 集成测试
- 完整的AI代码统计流程
- 钩子安装集成测试
- 配置文件集成测试
- 错误处理集成测试

### 边界情况测试
- 空输入和null值处理
- 无效输入验证
- 文件不存在错误
- 权限错误处理
- 极值测试
- 异常情况处理

## 运行测试

### 安装依赖
```bash
npm install
```

### 运行所有测试
```bash
npm test
```

### 运行特定测试文件
```bash
# 运行主要统计模块测试
npm test ai-code-stats.test.js

# 运行清理模块测试
npm test cleanup-markers.test.js

# 运行集成测试
npm test integration.test.js

# 运行边界情况测试
npm test edge-cases.test.js
```

### 监视模式运行测试
```bash
npm run test:watch
```

### 生成测试覆盖率报告
```bash
npm run test:coverage
```

### 详细模式运行测试
```bash
npm run test:verbose
```

## 测试覆盖率

测试套件设置了以下覆盖率阈值：
- 分支覆盖率: 80%
- 函数覆盖率: 80%
- 行覆盖率: 80%
- 语句覆盖率: 80%

覆盖率报告将生成在 `coverage/` 目录中，包含：
- 文本格式报告（控制台输出）
- HTML格式报告（`coverage/lcov-report/index.html`）
- LCOV格式报告（用于CI/CD集成）

## 测试工具和模拟

### MockFileSystem
模拟文件系统操作，支持：
- 文件内容设置和获取
- 文件存在性检查
- 目录创建和管理
- 文件读写操作模拟

### MockGitCommands
模拟Git命令执行，支持：
- 自定义命令响应设置
- 错误情况模拟
- 默认响应配置

### 测试数据固定装置
提供标准化的测试数据：
- 示例提交信息
- 示例仓库信息
- 示例代码统计数据
- 包含/不包含AI标记的文件内容

## 持续集成

### GitHub Actions 配置示例
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm test
      - run: npm run test:coverage
```

### 本地预提交钩子
```bash
# 安装预提交钩子
npm run install-hooks

# 或使用Husky
npm run install-husky
```

## 调试测试

### 运行单个测试用例
```bash
npx jest -t "应该正确加载默认配置"
```

### 调试模式运行
```bash
node --inspect-brk node_modules/.bin/jest --runInBand
```

### 查看详细错误信息
```bash
npm test -- --verbose --no-coverage
```

## 测试最佳实践

1. **独立性**: 每个测试用例都是独立的，不依赖其他测试的状态
2. **可重复性**: 测试结果在任何环境下都应该一致
3. **清晰性**: 测试用例名称清楚描述测试的功能
4. **完整性**: 覆盖正常情况、边界情况和错误情况
5. **维护性**: 使用模拟对象和固定装置简化测试维护

## 故障排除

### 常见问题

1. **模块缓存问题**: 确保在每个测试前清理模块缓存
2. **异步操作**: 使用适当的async/await或Promise处理
3. **模拟对象**: 确保在测试后恢复所有模拟
4. **文件路径**: 使用相对路径和模拟的文件系统

### 获取帮助

如果遇到测试相关问题：
1. 检查Jest配置文件
2. 查看测试输出的详细错误信息
3. 确认模拟对象设置正确
4. 检查测试环境设置

## 贡献指南

添加新测试时请遵循：
1. 使用描述性的测试名称
2. 遵循现有的测试结构
3. 添加适当的模拟和断言
4. 确保测试覆盖率不降低
5. 更新相关文档
