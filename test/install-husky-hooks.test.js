/**
 * install-husky-hooks.js 模块测试
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { MockFileSystem, MockGitCommands } = require('./helpers/mocks');
const { samplePackageJson } = require('./helpers/fixtures');

// 模拟依赖
jest.mock('fs');
jest.mock('path');
jest.mock('child_process');

describe('install-husky-hooks.js', () => {
  let mockFS;
  let mockGit;
  let originalProcessArgv;
  let originalProcessCwd;
  let installHuskyHooks;

  beforeEach(() => {
    // 保存原始值
    originalProcessArgv = process.argv;
    originalProcessCwd = process.cwd;

    // 初始化模拟对象
    mockFS = new MockFileSystem();
    mockGit = new MockGitCommands();

    // 设置模拟
    mockFS.mockAll();
    mockGit.mockExecSync();

    // 模拟process.cwd
    process.cwd = jest.fn().mockReturnValue('/mock/repo/path');

    // 模拟path.resolve
    jest.spyOn(path, 'resolve').mockImplementation((dir, file) => {
      return `/mock/absolute/path/${file}`;
    });

    // 模拟path.join
    jest.spyOn(path, 'join').mockImplementation((...parts) => {
      return parts.join('/');
    });

    // 设置默认的package.json
    mockFS.setFile('/mock/repo/path/package.json', JSON.stringify(samplePackageJson));

    // 清理模块缓存
    delete require.cache[require.resolve('../lib/install-husky-hooks.js')];
  });

  afterEach(() => {
    // 恢复原始值
    process.argv = originalProcessArgv;
    process.cwd = originalProcessCwd;

    // 清理模拟
    jest.clearAllMocks();
    mockGit.clear();
    mockFS.clear();
  });

  describe('基本Husky钩子安装功能', () => {
    test('应该读取package.json文件', () => {
      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 验证：应该读取package.json
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/package.json', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该创建.husky目录如果不存在', () => {
      // 设置：.husky目录不存在
      mockFS.setFile('/mock/repo/path/.husky', undefined);

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 验证：应该检查.husky目录是否存在
      expect(fs.existsSync).toHaveBeenCalledWith('/mock/repo/path/.husky');

      // 验证：应该创建.husky目录
      expect(fs.mkdirSync).toHaveBeenCalledWith('/mock/repo/path/.husky', { recursive: true });

      consoleSpy.mockRestore();
    });

    test('应该创建pre-commit钩子文件', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 验证：应该写入pre-commit钩子文件
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/repo/path/.husky/pre-commit',
        expect.stringContaining('#!/usr/bin/env sh'),
        undefined
      );

      // 验证：应该设置文件权限
      expect(fs.chmodSync).toHaveBeenCalledWith('/mock/repo/path/.husky/pre-commit', '755');

      consoleSpy.mockRestore();
    });

    test('应该更新package.json的scripts', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 验证：应该写入更新后的package.json
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        '/mock/repo/path/package.json',
        expect.stringContaining('"prepare"'),
        'utf-8'
      );

      consoleSpy.mockRestore();
    });
  });

  describe('钩子内容验证', () => {
    test('pre-commit钩子应该包含正确的内容', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.husky/pre-commit'
      )[1];

      // 验证：应该包含正确的shebang
      expect(hookContent).toContain('#!/usr/bin/env sh');

      // 验证：应该包含husky.sh引用
      expect(hookContent).toContain('. "$(dirname -- "$0")/_/husky.sh"');

      // 验证：应该包含AI代码统计逻辑
      expect(hookContent).toContain('AI代码统计和清理');
      expect(hookContent).toContain('ai-code-stats.js');
      expect(hookContent).toContain('--mode=pre-commit');

      consoleSpy.mockRestore();
    });

    test('应该在钩子中包含正确的脚本路径', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.husky/pre-commit'
      )[1];

      // 验证：应该包含正确的脚本路径
      expect(hookContent).toContain('/mock/absolute/path/ai-code-stats.js');

      consoleSpy.mockRestore();
    });
  });

  describe('package.json更新', () => {
    test('应该添加prepare脚本如果不存在', () => {
      // 设置：package.json没有scripts
      const packageWithoutScripts = { name: 'test', version: '1.0.0' };
      mockFS.setFile('/mock/repo/path/package.json', JSON.stringify(packageWithoutScripts));

      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 获取写入的package.json内容
      const packageContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/package.json'
      )[1];

      const updatedPackage = JSON.parse(packageContent);

      // 验证：应该添加scripts对象和prepare脚本
      expect(updatedPackage.scripts).toBeDefined();
      expect(updatedPackage.scripts.prepare).toBe('husky install');

      consoleSpy.mockRestore();
    });

    test('应该保留现有的scripts', () => {
      // 设置：package.json有现有的scripts
      const packageWithScripts = {
        name: 'test',
        version: '1.0.0',
        scripts: {
          test: 'jest',
          build: 'webpack'
        }
      };
      mockFS.setFile('/mock/repo/path/package.json', JSON.stringify(packageWithScripts));

      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 获取写入的package.json内容
      const packageContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/package.json'
      )[1];

      const updatedPackage = JSON.parse(packageContent);

      // 验证：应该保留现有scripts并添加prepare
      expect(updatedPackage.scripts.test).toBe('jest');
      expect(updatedPackage.scripts.build).toBe('webpack');
      expect(updatedPackage.scripts.prepare).toBe('husky install');

      consoleSpy.mockRestore();
    });
  });

  describe('现有钩子处理', () => {
    test('应该合并现有的pre-commit钩子内容', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 设置：现有的pre-commit钩子
      const existingHook = `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm test`;

      mockFS.setFile('/mock/repo/path/.husky/pre-commit', existingHook);

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.husky/pre-commit'
      )[1];

      // 验证：应该包含现有内容和新的AI统计内容
      expect(hookContent).toContain('npm test');
      expect(hookContent).toContain('AI代码统计和清理');

      consoleSpy.mockRestore();
    });

    test('应该创建新钩子如果不存在', () => {
      // 设置：.husky目录存在但没有pre-commit钩子
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 获取钩子内容
      const hookContent = fs.writeFileSync.mock.calls.find(call => 
        call[0] === '/mock/repo/path/.husky/pre-commit'
      )[1];

      // 验证：应该创建完整的新钩子
      expect(hookContent).toContain('#!/usr/bin/env sh');
      expect(hookContent).toContain('. "$(dirname -- "$0")/_/husky.sh"');
      expect(hookContent).toContain('AI代码统计和清理');

      consoleSpy.mockRestore();
    });
  });

  describe('错误处理', () => {
    test('应该处理package.json读取错误', () => {
      // 设置：package.json不存在
      mockFS.setFile('/mock/repo/path/package.json', undefined);

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试加载模块
      expect(() => {
        installHuskyHooks = require('../lib/install-husky-hooks.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });

    test('应该处理package.json格式错误', () => {
      // 设置：格式错误的package.json
      mockFS.setFile('/mock/repo/path/package.json', '{ invalid json }');

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试加载模块
      expect(() => {
        installHuskyHooks = require('../lib/install-husky-hooks.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });

    test('应该处理文件写入错误', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟文件写入失败
      jest.spyOn(fs, 'writeFileSync').mockImplementation((filePath) => {
        if (filePath.includes('pre-commit')) {
          throw new Error('Permission denied');
        }
      });

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试加载模块
      expect(() => {
        installHuskyHooks = require('../lib/install-husky-hooks.js');
      }).toThrow('Permission denied');

      consoleSpy.mockRestore();
    });
  });

  describe('输出消息', () => {
    test('应该输出安装成功消息', () => {
      // 设置：.husky目录存在
      mockFS.directories.add('/mock/repo/path/.husky');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      installHuskyHooks = require('../lib/install-husky-hooks.js');

      // 验证：应该输出成功消息
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Pre-commit hook 已创建')
      );

      consoleSpy.mockRestore();
    });
  });
});
