/**
 * cleanup-markers.js 模块测试
 */

const fs = require('fs');
const { execSync } = require('child_process');
const { MockFileSystem, MockGitCommands } = require('./helpers/mocks');
const { fileWithAIMarkers, fileWithoutAIMarkers } = require('./helpers/fixtures');

// 模拟依赖
jest.mock('fs');
jest.mock('child_process');

describe('cleanup-markers.js', () => {
  let mockFS;
  let mockGit;
  let originalProcessArgv;
  let cleanupMarkers;

  beforeEach(() => {
    // 保存原始值
    originalProcessArgv = process.argv;

    // 初始化模拟对象
    mockFS = new MockFileSystem();
    mockGit = new MockGitCommands();

    // 设置模拟
    mockFS.mockAll();
    mockGit.mockExecSync();

    // 清理模块缓存
    delete require.cache[require.resolve('../lib/cleanup-markers.js')];
  });

  afterEach(() => {
    // 恢复原始值
    process.argv = originalProcessArgv;

    // 清理模拟
    jest.clearAllMocks();
    mockGit.clear();
    mockFS.clear();
  });

  describe('基本清理功能', () => {
    test('应该清理包含AI标记的文件', () => {
      // 设置：创建包含AI标记的文件
      mockFS.setFile('./src/main.js', fileWithAIMarkers);
      mockFS.setFile('./src/utils.js', fileWithoutAIMarkers);

      // 设置find命令响应
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/main.js\n./src/utils.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块（这会触发清理逻辑）
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该读取文件
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/main.js', 'utf-8');
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/utils.js', 'utf-8');

      // 验证：应该写入清理后的文件
      expect(fs.writeFileSync).toHaveBeenCalledWith('./src/main.js', expect.any(String), 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该跳过不包含AI标记的文件', () => {
      // 设置：创建不包含AI标记的文件
      mockFS.setFile('./src/clean.js', fileWithoutAIMarkers);

      // 设置find命令响应
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/clean.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该读取文件但不写入
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/clean.js', 'utf-8');
      expect(fs.writeFileSync).not.toHaveBeenCalledWith('./src/clean.js', expect.any(String), 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该正确计算清理的标记数量', () => {
      // 设置：创建包含多个AI标记的文件
      const multipleMarkersContent = `
// Normal code
function test() {}

// [[GBAI START]]
// AI code block 1
function ai1() {}
// [[GBAI END]]

// More normal code
const config = {};

// [[GBAI START]]
// AI code block 2
function ai2() {}
// [[GBAI END]]
`;

      mockFS.setFile('./src/multiple.js', multipleMarkersContent);
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/multiple.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理文件
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/multiple.js', 'utf-8');
      expect(fs.writeFileSync).toHaveBeenCalledWith('./src/multiple.js', expect.any(String), 'utf-8');

      // 验证：清理后的内容不应包含标记
      const writtenContent = fs.writeFileSync.mock.calls[0][1];
      expect(writtenContent).not.toContain('[[GBAI START]]');
      expect(writtenContent).not.toContain('[[GBAI END]]');

      consoleSpy.mockRestore();
    });
  });

  describe('错误处理', () => {
    test('应该处理文件读取错误', () => {
      // 设置：模拟文件读取错误
      mockFS.setFile('./src/error.js', undefined); // 文件不存在

      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/error.js');

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该尝试读取文件
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/error.js', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该跳过二进制文件', () => {
      // 设置：模拟二进制文件读取错误
      jest.spyOn(fs, 'readFileSync').mockImplementation((filePath) => {
        if (filePath === './src/binary.bin') {
          const error = new Error('File is binary');
          error.code = 'EISDIR';
          throw error;
        }
        return mockFS.getFile(filePath);
      });

      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/binary.bin');

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该尝试读取文件但跳过错误
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/binary.bin', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该处理find命令失败', () => {
      // 设置：模拟find命令失败
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', 'Command failed', true);

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理错误
      expect(execSync).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('命令行参数处理', () => {
    test('应该支持静默模式', () => {
      // 设置命令行参数
      process.argv = ['node', 'cleanup-markers.js', '--quiet'];

      // 设置文件
      mockFS.setFile('./src/test.js', fileWithAIMarkers);
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/test.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：在静默模式下应该减少日志输出
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    test('应该支持详细模式', () => {
      // 设置命令行参数（默认为详细模式）
      process.argv = ['node', 'cleanup-markers.js'];

      // 设置文件
      mockFS.setFile('./src/test.js', fileWithAIMarkers);
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/test.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该有详细的日志输出
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('文件过滤', () => {
    test('应该跳过清理脚本自身', () => {
      // 设置文件列表，包含清理脚本自身
      mockFS.setFile('./lib/cleanup-markers.js', fileWithAIMarkers);
      mockFS.setFile('./src/test.js', fileWithAIMarkers);

      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './lib/cleanup-markers.js\n./src/test.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该跳过清理脚本自身
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/test.js', 'utf-8');
      expect(fs.readFileSync).not.toHaveBeenCalledWith('./lib/cleanup-markers.js', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该跳过Markdown文件', () => {
      // 设置文件列表，包含Markdown文件
      mockFS.setFile('./README.md', fileWithAIMarkers);
      mockFS.setFile('./src/test.js', fileWithAIMarkers);

      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './README.md\n./src/test.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该跳过Markdown文件
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/test.js', 'utf-8');
      expect(fs.readFileSync).not.toHaveBeenCalledWith('./README.md', 'utf-8');

      consoleSpy.mockRestore();
    });
  });

  describe('清理结果统计', () => {
    test('应该正确统计清理结果', () => {
      // 设置多个文件，部分包含AI标记
      mockFS.setFile('./src/with-ai.js', fileWithAIMarkers);
      mockFS.setFile('./src/without-ai.js', fileWithoutAIMarkers);
      mockFS.setFile('./src/empty.js', '');

      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './src/with-ai.js\n./src/without-ai.js\n./src/empty.js');

      // 模拟console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：加载模块
      cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理所有文件
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/with-ai.js', 'utf-8');
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/without-ai.js', 'utf-8');
      expect(fs.readFileSync).toHaveBeenCalledWith('./src/empty.js', 'utf-8');

      // 验证：只有包含AI标记的文件被写入
      expect(fs.writeFileSync).toHaveBeenCalledWith('./src/with-ai.js', expect.any(String), 'utf-8');
      expect(fs.writeFileSync).not.toHaveBeenCalledWith('./src/without-ai.js', expect.any(String), 'utf-8');

      consoleSpy.mockRestore();
    });
  });
});
