/**
 * Jest 测试环境设置文件
 * 在每个测试文件运行前执行
 */

// 设置测试超时时间
jest.setTimeout(10000);

// 全局测试配置
global.console = {
  ...console,
  // 在测试中静默某些日志输出
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn()
};

// 测试前的全局设置
beforeEach(() => {
  // 清理所有模拟
  jest.clearAllMocks();

  // 模拟process.exit
  jest.spyOn(process, 'exit').mockImplementation(() => {
    throw new Error('process.exit called');
  });

  // 重置环境变量
  delete process.env.NODE_ENV;
  delete process.env.AI_STATS_CONFIG;
});

// 测试后的全局清理
afterEach(() => {
  // 恢复所有模拟
  jest.restoreAllMocks();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
