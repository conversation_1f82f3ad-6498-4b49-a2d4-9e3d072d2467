/**
 * 边界情况和错误处理测试
 */

const fs = require('fs');
const { execSync } = require('child_process');
const { MockFileSystem, MockGitCommands, createTestConfig } = require('./helpers/mocks');

// 模拟依赖
jest.mock('fs');
jest.mock('child_process');

describe('边界情况和错误处理测试', () => {
  let mockFS;
  let mockGit;
  let originalProcessArgv;
  let originalProcessCwd;

  beforeEach(() => {
    // 保存原始值
    originalProcessArgv = process.argv;
    originalProcessCwd = process.cwd;

    // 初始化模拟对象
    mockFS = new MockFileSystem();
    mockGit = new MockGitCommands();

    // 设置模拟
    mockFS.mockAll();
    mockGit.mockExecSync();

    // 模拟process.cwd
    process.cwd = jest.fn().mockReturnValue('/mock/repo/path');

    // 清理模块缓存
    Object.keys(require.cache).forEach(key => {
      if (key.includes('lib/')) {
        delete require.cache[key];
      }
    });
  });

  afterEach(() => {
    // 恢复原始值
    process.argv = originalProcessArgv;
    process.cwd = originalProcessCwd;

    // 清理模拟
    jest.clearAllMocks();
    mockGit.clear();
    mockFS.clear();
  });

  describe('空输入和null值处理', () => {
    test('应该处理空的Git diff输出', () => {
      // 设置：空的diff输出
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, '');
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${commitHash}`, 'Test Author <<EMAIL>>');
      mockGit.setCommandResponse(`git show -s --format="%B" ${commitHash}`, 'Empty commit');
      mockGit.setCommandResponse(`git show -s --format="%at" ${commitHash}`, '1701432000');

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理空输出
      expect(execSync).toHaveBeenCalledWith(`git diff --numstat ${commitHash}^..${commitHash}`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该处理空的文件列表', () => {
      // 设置：空的文件列表
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, '');

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理空文件列表
      expect(execSync).toHaveBeenCalledWith(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该处理空的文件内容', () => {
      // 设置：空文件内容
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'empty.js');
      mockGit.setCommandResponse(`git show ${commitHash}:empty.js`, '');

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理空文件
      expect(execSync).toHaveBeenCalledWith(`git show ${commitHash}:empty.js`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该处理null和undefined配置值', () => {
      // 设置：包含null值的配置
      const configWithNulls = {
        enabled: null,
        autoCleanup: undefined,
        verbose: true,
        api: null,
        ignore: {
          files: null,
          patterns: undefined
        }
      };
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(configWithNulls));

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js'];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理null值配置
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');

      consoleSpy.mockRestore();
    });
  });

  describe('无效输入验证', () => {
    test('应该处理无效的提交哈希', () => {
      // 设置：无效的提交哈希
      const invalidHash = 'invalid-hash-123';
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${invalidHash}`, 'Command failed', true);

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', invalidHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试运行AI代码统计
      expect(() => {
        const aiCodeStats = require('../lib/ai-code-stats.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });

    test('应该处理格式错误的Git输出', () => {
      // 设置：格式错误的diff输出
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, 'invalid\tformat\tlines\twith\ttoo\tmany\tfields');
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${commitHash}`, 'Test Author <<EMAIL>>');
      mockGit.setCommandResponse(`git show -s --format="%B" ${commitHash}`, 'Test commit');
      mockGit.setCommandResponse(`git show -s --format="%at" ${commitHash}`, '1701432000');

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理格式错误的输出
      expect(execSync).toHaveBeenCalledWith(`git diff --numstat ${commitHash}^..${commitHash}`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该处理非数字的统计值', () => {
      // 设置：包含非数字值的diff输出
      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, '-\t-\tbinary-file.bin\nabc\tdef\tinvalid.js');
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${commitHash}`, 'Test Author <<EMAIL>>');
      mockGit.setCommandResponse(`git show -s --format="%B" ${commitHash}`, 'Test commit');
      mockGit.setCommandResponse(`git show -s --format="%at" ${commitHash}`, '1701432000');

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理非数字统计值
      expect(execSync).toHaveBeenCalledWith(`git diff --numstat ${commitHash}^..${commitHash}`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });
  });

  describe('文件不存在错误', () => {
    test('应该处理配置文件不存在', () => {
      // 设置：配置文件不存在
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', undefined);

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js'];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该使用默认配置
      expect(fs.existsSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json');

      consoleSpy.mockRestore();
    });

    test('应该处理Git仓库不存在', () => {
      // 设置：Git命令失败（不是Git仓库）
      mockGit.setCommandResponse('git rev-parse --show-toplevel', 'fatal: not a git repository', true);

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试安装钩子
      expect(() => {
        const installHooks = require('../lib/install-hooks.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });

    test('应该处理package.json不存在', () => {
      // 设置：package.json不存在
      mockFS.setFile('/mock/repo/path/package.json', undefined);

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试安装Husky钩子
      expect(() => {
        const installHuskyHooks = require('../lib/install-husky-hooks.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });
  });

  describe('权限错误处理', () => {
    test('应该处理文件读取权限错误', () => {
      // 设置：文件读取权限错误
      jest.spyOn(fs, 'readFileSync').mockImplementation((filePath) => {
        if (filePath.includes('permission-denied.js')) {
          const error = new Error('EACCES: permission denied');
          error.code = 'EACCES';
          throw error;
        }
        return mockFS.getFile(filePath);
      });

      // 设置find命令响应
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './permission-denied.js');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：运行清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理权限错误
      expect(fs.readFileSync).toHaveBeenCalledWith('./permission-denied.js', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该处理文件写入权限错误', () => {
      // 设置：文件写入权限错误
      mockFS.setFile('./readonly.js', '// [[GBAI START]]\n// AI code\n// [[GBAI END]]');
      
      jest.spyOn(fs, 'writeFileSync').mockImplementation((filePath) => {
        if (filePath.includes('readonly.js')) {
          const error = new Error('EACCES: permission denied');
          error.code = 'EACCES';
          throw error;
        }
      });

      // 设置find命令响应
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './readonly.js');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：运行清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该尝试写入文件
      expect(fs.writeFileSync).toHaveBeenCalledWith('./readonly.js', expect.any(String), 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该处理目录创建权限错误', () => {
      // 设置：目录创建权限错误
      jest.spyOn(fs, 'mkdirSync').mockImplementation(() => {
        const error = new Error('EACCES: permission denied');
        error.code = 'EACCES';
        throw error;
      });

      // 设置：hooks目录不存在
      mockFS.setFile('/mock/repo/path/.git/hooks', undefined);
      mockGit.setCommandResponse('git rev-parse --show-toplevel', '/mock/repo/path');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：尝试安装钩子
      expect(() => {
        const installHooks = require('../lib/install-hooks.js');
      }).toThrow();

      consoleSpy.mockRestore();
    });
  });

  describe('极值测试', () => {
    test('应该处理极大的文件', () => {
      // 设置：创建极大的文件内容
      const largeContent = Array(10000).fill('// Large file line').join('\n');
      const commitHash = 'abc123def456';
      
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'large-file.js');
      mockGit.setCommandResponse(`git show ${commitHash}:large-file.js`, largeContent);

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理大文件
      expect(execSync).toHaveBeenCalledWith(`git show ${commitHash}:large-file.js`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该处理极多的AI标记', () => {
      // 设置：创建包含大量AI标记的文件
      const manyMarkers = Array(1000).fill().map((_, i) => 
        `// [[GBAI START]]\n// AI block ${i}\n// [[GBAI END]]`
      ).join('\n');

      mockFS.setFile('./many-markers.js', manyMarkers);
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './many-markers.js');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理大量标记
      expect(fs.readFileSync).toHaveBeenCalledWith('./many-markers.js', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该处理极长的文件路径', () => {
      // 设置：极长的文件路径
      const longPath = './very/deep/nested/directory/structure/with/many/levels/and/a/very/long/filename/that/exceeds/normal/limits.js';
      mockFS.setFile(longPath, '// Normal file');
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', longPath);

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理长路径
      expect(fs.readFileSync).toHaveBeenCalledWith(longPath, 'utf-8');

      consoleSpy.mockRestore();
    });
  });

  describe('异常情况处理', () => {
    test('应该处理不完整的AI标记', () => {
      // 设置：不完整的AI标记（只有开始标记）
      const incompleteMarkers = `
// Normal code
// [[GBAI START]]
// AI code without end marker
function incomplete() {}
`;

      const commitHash = 'abc123def456';
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'incomplete.js');
      mockGit.setCommandResponse(`git show ${commitHash}:incomplete.js`, incompleteMarkers);

      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', commitHash];

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行AI代码统计
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理不完整的标记
      expect(execSync).toHaveBeenCalledWith(`git show ${commitHash}:incomplete.js`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该处理嵌套的AI标记', () => {
      // 设置：嵌套的AI标记
      const nestedMarkers = `
// [[GBAI START]]
// Outer AI block
// [[GBAI START]]
// Inner AI block (invalid nesting)
// [[GBAI END]]
// [[GBAI END]]
`;

      mockFS.setFile('./nested.js', nestedMarkers);
      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './nested.js');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // 执行：运行清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理嵌套标记
      expect(fs.readFileSync).toHaveBeenCalledWith('./nested.js', 'utf-8');

      consoleSpy.mockRestore();
    });

    test('应该处理系统资源不足', () => {
      // 设置：模拟内存不足错误
      jest.spyOn(fs, 'readFileSync').mockImplementation(() => {
        const error = new Error('ENOMEM: not enough memory');
        error.code = 'ENOMEM';
        throw error;
      });

      mockGit.setCommandResponse('find . -type f -not -path "./.git/*"', './memory-test.js');

      // 模拟console输出
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 执行：运行清理标记
      const cleanupMarkers = require('../lib/cleanup-markers.js');

      // 验证：应该处理内存错误
      expect(fs.readFileSync).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });
});
