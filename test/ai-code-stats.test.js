/**
 * ai-code-stats.js 模块测试
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { MockFileSystem, MockGitCommands, createTestConfig } = require('./helpers/mocks');
const { 
  sampleCommitInfo, 
  sampleRepoInfo, 
  sampleCodeStats,
  sampleGitDiffOutput,
  sampleGitLogOutput,
  fileWithAIMarkers,
  fileWithoutAIMarkers
} = require('./helpers/fixtures');

// 模拟ai-code-stats模块
jest.mock('child_process');
jest.mock('fs');
jest.mock('path');

describe('ai-code-stats.js', () => {
  let mockFS;
  let mockGit;
  let originalProcessArgv;
  let originalProcessCwd;

  beforeEach(() => {
    // 保存原始值
    originalProcessArgv = process.argv;
    originalProcessCwd = process.cwd;

    // 初始化模拟对象
    mockFS = new MockFileSystem();
    mockGit = new MockGitCommands();

    // 设置模拟
    mockFS.mockAll();
    mockGit.mockExecSync();

    // 模拟process.cwd
    process.cwd = jest.fn().mockReturnValue('/mock/repo/path');

    // 清理模块缓存
    delete require.cache[require.resolve('../lib/ai-code-stats.js')];
  });

  afterEach(() => {
    // 恢复原始值
    process.argv = originalProcessArgv;
    process.cwd = originalProcessCwd;

    // 清理模拟
    jest.clearAllMocks();
    mockGit.clear();
    mockFS.clear();
  });

  describe('配置加载', () => {
    test('应该加载默认配置当配置文件不存在时', () => {
      // 设置：配置文件不存在
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', undefined);

      // 重新加载模块以触发配置加载
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该使用默认配置
      expect(fs.existsSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json');
    });

    test('应该加载用户配置当配置文件存在时', () => {
      // 设置：创建配置文件
      const userConfig = createTestConfig({
        verbose: false,
        api: { enabled: true, url: 'https://custom-api.com' }
      });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(userConfig));

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该读取配置文件
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');
    });

    test('应该使用默认配置当配置文件格式错误时', () => {
      // 设置：创建格式错误的配置文件
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', '{ invalid json }');

      // 模拟console.warn
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该输出警告并使用默认配置
      expect(consoleSpy).toHaveBeenCalledWith('⚠️  配置文件格式错误，使用默认配置');
      
      consoleSpy.mockRestore();
    });
  });

  describe('深度合并功能', () => {
    test('应该正确合并嵌套对象', () => {
      // 由于deepMerge是内部函数，我们通过配置加载来测试它
      const userConfig = {
        api: {
          enabled: true,
          url: 'https://custom-api.com'
          // headers 应该从默认配置继承
        },
        ignore: {
          files: ['custom-file.json']
          // patterns 应该从默认配置继承
        }
      };

      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(userConfig));

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：配置应该被正确合并
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');
    });
  });

  describe('仓库信息获取', () => {
    test('应该正确解析SSH格式的仓库URL', () => {
      // 设置Git命令响应
      mockGit.setCommandResponse('git config --get remote.origin.url', '************************:test/repo.git');
      mockGit.setCommandResponse('git rev-parse --abbrev-ref HEAD', 'main');

      // 重新加载模块并测试getRepoInfo函数
      // 注意：由于getRepoInfo是内部函数，我们需要通过其他方式测试
      // 这里我们验证Git命令被正确调用
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用正确的Git命令
      expect(execSync).toHaveBeenCalledWith('git config --get remote.origin.url', { encoding: 'utf-8' });
      expect(execSync).toHaveBeenCalledWith('git rev-parse --abbrev-ref HEAD', { encoding: 'utf-8' });
    });

    test('应该正确解析HTTPS格式的仓库URL', () => {
      // 设置Git命令响应
      mockGit.setCommandResponse('git config --get remote.origin.url', 'https://code.alibaba-inc.com/test/repo.git');
      mockGit.setCommandResponse('git rev-parse --abbrev-ref HEAD', 'develop');

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用正确的Git命令
      expect(execSync).toHaveBeenCalledWith('git config --get remote.origin.url', { encoding: 'utf-8' });
    });
  });

  describe('提交信息获取', () => {
    test('应该正确获取提交信息', () => {
      const commitHash = 'abc123def456';
      
      // 设置Git命令响应
      mockGit.setCommandResponse(`git show -s --format="%an <%ae>" ${commitHash}`, 'Test Author <<EMAIL>>');
      mockGit.setCommandResponse(`git show -s --format="%B" ${commitHash}`, 'Test commit message');
      mockGit.setCommandResponse(`git show -s --format="%at" ${commitHash}`, '1701432000');

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用正确的Git命令
      expect(execSync).toHaveBeenCalledWith(`git show -s --format="%an <%ae>" ${commitHash}`, { encoding: 'utf-8' });
      expect(execSync).toHaveBeenCalledWith(`git show -s --format="%B" ${commitHash}`, { encoding: 'utf-8' });
      expect(execSync).toHaveBeenCalledWith(`git show -s --format="%at" ${commitHash}`, { encoding: 'utf-8' });
    });
  });

  describe('文件忽略逻辑', () => {
    test('应该忽略配置中指定的文件', () => {
      // 设置配置文件
      const config = createTestConfig({
        ignore: {
          files: ['package.json', 'yarn.lock'],
          patterns: ['*.log', 'node_modules/**']
        }
      });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(config));

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 由于shouldIgnoreFile是内部函数，我们通过其他方式验证
      // 这里验证配置被正确加载
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');
    });
  });

  describe('代码统计功能', () => {
    test('应该正确解析git diff输出', () => {
      const commitHash = 'abc123def456';
      
      // 设置Git命令响应
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, sampleGitDiffOutput);

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用正确的Git命令
      expect(execSync).toHaveBeenCalledWith(`git diff --numstat ${commitHash}^..${commitHash}`, { encoding: 'utf-8' });
    });

    test('应该处理空的diff输出', () => {
      const commitHash = 'abc123def456';
      
      // 设置空的Git命令响应
      mockGit.setCommandResponse(`git diff --numstat ${commitHash}^..${commitHash}`, '');

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用Git命令
      expect(execSync).toHaveBeenCalledWith(`git diff --numstat ${commitHash}^..${commitHash}`, { encoding: 'utf-8' });
    });
  });

  describe('AI代码统计功能', () => {
    test('应该正确识别AI代码标记', () => {
      const commitHash = 'abc123def456';

      // 设置Git命令响应
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'src/main.js\nsrc/utils.js');
      mockGit.setCommandResponse(`git show ${commitHash}:src/main.js`, fileWithAIMarkers);
      mockGit.setCommandResponse(`git show ${commitHash}:src/utils.js`, fileWithoutAIMarkers);

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用正确的Git命令
      expect(execSync).toHaveBeenCalledWith(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, { encoding: 'utf-8' });
    });

    test('应该处理Git命令失败的情况', () => {
      const commitHash = 'invalid-hash';

      // 设置Git命令失败
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'Command failed', true);

      // 模拟console.error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该处理错误
      expect(execSync).toHaveBeenCalledWith(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, { encoding: 'utf-8' });

      consoleSpy.mockRestore();
    });

    test('应该正确计算AI代码行数', () => {
      const commitHash = 'abc123def456';

      // 创建包含多个AI代码块的文件内容
      const multipleAIBlocks = `
// Normal code
function normal() {}

// [[GBAI START]]
// AI block 1
function ai1() {}
// [[GBAI END]]

// More normal code
const config = {};

// [[GBAI START]]
// AI block 2
function ai2() {}
function ai3() {}
// [[GBAI END]]
`;

      // 设置Git命令响应
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'src/main.js');
      mockGit.setCommandResponse(`git show ${commitHash}:src/main.js`, multipleAIBlocks);

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该调用正确的Git命令
      expect(execSync).toHaveBeenCalledWith(`git show ${commitHash}:src/main.js`, { encoding: 'utf-8' });
    });

    test('应该忽略包管理文件', () => {
      const commitHash = 'abc123def456';

      // 设置Git命令响应，包含包管理文件
      mockGit.setCommandResponse(`git diff-tree --no-commit-id --name-only -r ${commitHash}`, 'src/main.js\npackage.json\nyarn.lock');
      mockGit.setCommandResponse(`git show ${commitHash}:src/main.js`, fileWithAIMarkers);

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：应该只处理非包管理文件
      expect(execSync).toHaveBeenCalledWith(`git show ${commitHash}:src/main.js`, { encoding: 'utf-8' });
      expect(execSync).not.toHaveBeenCalledWith(`git show ${commitHash}:package.json`, { encoding: 'utf-8' });
    });
  });

  describe('命令行参数处理', () => {
    test('应该正确解析详细模式参数', () => {
      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', '--detailed'];

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：参数应该被正确解析
      // 由于参数解析在模块加载时进行，我们验证模块被正确加载
      expect(aiCodeStats).toBeDefined();
    });

    test('应该正确解析静默模式参数', () => {
      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', '--quiet'];

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：参数应该被正确解析
      expect(aiCodeStats).toBeDefined();
    });

    test('应该正确解析pre-commit模式参数', () => {
      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', '--mode=pre-commit'];

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：参数应该被正确解析
      expect(aiCodeStats).toBeDefined();
    });

    test('应该正确解析提交哈希参数', () => {
      // 设置命令行参数
      process.argv = ['node', 'ai-code-stats.js', 'abc123def456', '--detailed'];

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：参数应该被正确解析
      expect(aiCodeStats).toBeDefined();
    });
  });

  describe('Glob模式匹配', () => {
    test('应该正确转换简单通配符', () => {
      // 由于globToRegex是内部函数，我们通过文件忽略逻辑来测试
      const config = createTestConfig({
        ignore: {
          files: [],
          patterns: ['*.log', '*.tmp']
        }
      });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(config));

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：配置被正确加载
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');
    });

    test('应该正确转换双星号通配符', () => {
      const config = createTestConfig({
        ignore: {
          files: [],
          patterns: ['node_modules/**', '.git/**']
        }
      });
      mockFS.setFile('/mock/repo/path/.ai-code-stats.json', JSON.stringify(config));

      // 重新加载模块
      const aiCodeStats = require('../lib/ai-code-stats.js');

      // 验证：配置被正确加载
      expect(fs.readFileSync).toHaveBeenCalledWith('/mock/repo/path/.ai-code-stats.json', 'utf-8');
    });
  });
});
