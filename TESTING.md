# AI Code Stats 测试套件使用指南

## 概述

本项目已配置了完整的测试套件，包括单元测试、集成测试、边界情况测试和错误处理测试。测试框架使用 Jest，提供了全面的代码覆盖率报告和持续集成支持。

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 运行基本测试验证环境
```bash
npm test basic.test.js
```

### 3. 运行所有测试
```bash
npm test
```

### 4. 生成覆盖率报告
```bash
npm run test:coverage
```

## 测试结构

```
test/
├── README.md                 # 详细测试文档
├── setup.js                  # Jest 环境设置
├── basic.test.js             # 基本功能验证测试
├── helpers/                  # 测试辅助工具
│   ├── mocks.js             # 模拟对象和工具
│   └── fixtures.js          # 测试数据
├── ai-code-stats.test.js    # 主模块测试
├── cleanup-markers.test.js  # 清理模块测试
├── install-hooks.test.js    # Git钩子安装测试
├── install-husky-hooks.test.js # Husky钩子测试
├── integration.test.js      # 集成测试
└── edge-cases.test.js       # 边界情况测试
```

## 可用的测试命令

| 命令 | 描述 |
|------|------|
| `npm test` | 运行所有测试 |
| `npm run test:watch` | 监视模式运行测试 |
| `npm run test:coverage` | 生成覆盖率报告 |
| `npm run test:verbose` | 详细模式运行测试 |
| `npm test basic.test.js` | 运行特定测试文件 |

## 测试覆盖范围

### ✅ 已实现的测试

1. **基本功能测试** (`basic.test.js`)
   - 文件系统操作模拟
   - 命令执行模拟
   - 进程退出处理
   - 控制台输出模拟

2. **测试工具和模拟** (`helpers/`)
   - MockFileSystem: 文件系统模拟
   - MockGitCommands: Git命令模拟
   - 测试数据固定装置

3. **单元测试框架**
   - ai-code-stats.js 模块测试
   - cleanup-markers.js 模块测试
   - install-hooks.js 模块测试
   - install-husky-hooks.js 模块测试

4. **集成测试**
   - 端到端工作流程测试
   - 钩子安装集成测试
   - 配置文件集成测试

5. **边界情况测试**
   - 空输入处理
   - 无效输入验证
   - 文件不存在错误
   - 权限错误处理
   - 极值测试

### 🔧 需要修复的问题

当前测试套件遇到以下问题，需要进一步修复：

1. **模块加载问题**: 源代码模块在测试时会立即执行，导致process.exit被调用
2. **模拟设置**: 需要更好的模拟策略来隔离模块执行
3. **语法错误**: 已修复install-husky-hooks.js中的语法错误

## 测试配置

### Jest 配置 (`jest.config.js`)
- 测试环境: Node.js
- 覆盖率阈值: 80%
- 超时时间: 10秒
- 自动清理模拟

### 覆盖率报告
- 文本格式（控制台）
- HTML格式（`coverage/lcov-report/index.html`）
- LCOV格式（CI/CD集成）

## 持续集成

### GitHub Actions (`.github/workflows/test.yml`)
- 多Node.js版本测试 (16.x, 18.x, 20.x)
- 自动化测试运行
- 覆盖率报告上传
- 安全扫描
- 性能测试

## 下一步改进建议

### 1. 修复现有测试
```bash
# 需要重构测试以避免模块立即执行问题
# 建议使用动态导入或模块工厂模式
```

### 2. 增加功能测试
```bash
# 添加更多实际场景测试
# 测试真实的Git仓库操作
```

### 3. 性能测试
```bash
# 添加大文件处理性能测试
# 测试内存使用情况
```

### 4. 端到端测试
```bash
# 创建真实的Git仓库进行测试
# 测试完整的用户工作流程
```

## 故障排除

### 常见问题

1. **测试失败: process.exit called**
   - 原因: 模块在加载时立即执行
   - 解决: 使用模拟的process.exit或重构模块结构

2. **模拟不生效**
   - 原因: 模块缓存或模拟时机问题
   - 解决: 清理模块缓存，正确设置模拟

3. **覆盖率过低**
   - 原因: 测试未实际执行源代码
   - 解决: 修复测试以正确调用源代码函数

### 调试技巧

```bash
# 运行单个测试
npx jest -t "测试名称"

# 调试模式
node --inspect-brk node_modules/.bin/jest --runInBand

# 查看详细输出
npm test -- --verbose --no-coverage
```

## 贡献指南

1. 添加新测试时遵循现有结构
2. 使用描述性的测试名称
3. 确保测试独立且可重复
4. 添加适当的模拟和断言
5. 更新相关文档

## 总结

虽然测试套件的框架已经完整建立，但由于源代码的结构特点（模块立即执行），当前的测试需要进一步重构才能正常运行。建议的改进方向包括：

1. 重构源代码以支持更好的测试
2. 改进模拟策略
3. 添加更多实际场景测试
4. 完善持续集成流程

测试套件为项目提供了坚实的质量保证基础，随着进一步的完善，将能够确保代码的可靠性和稳定性。
