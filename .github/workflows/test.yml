name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: |
        # 如果有ESLint配置，运行代码检查
        if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
          npm run lint
        fi
      continue-on-error: true
      
    - name: Run tests
      run: npm test
      
    - name: Run tests with coverage
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
        
    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          coverage/
          test-results.xml
        retention-days: 30
        
    - name: Archive coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-reports-${{ matrix.node-version }}
        path: coverage/
        retention-days: 30

  integration-test:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup Git for testing
      run: |
        git config --global user.name "Test User"
        git config --global user.email "<EMAIL>"
        git init
        git add .
        git commit -m "Initial commit for testing"
        
    - name: Test CLI commands
      run: |
        # 测试主要命令
        node lib/ai-code-stats.js --help || true
        node lib/cleanup-markers.js --quiet || true
        
    - name: Test hook installation
      run: |
        # 测试钩子安装
        node lib/install-hooks.js || true
        
    - name: Test Husky integration
      run: |
        # 测试Husky集成
        npm install husky --save-dev
        node lib/install-husky-hooks.js || true

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
      
    - name: Check for vulnerabilities
      run: |
        # 检查已知漏洞
        npx audit-ci --moderate
      continue-on-error: true

  performance-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Create large test repository
      run: |
        # 创建大型测试仓库
        mkdir large-repo
        cd large-repo
        git init
        git config user.name "Test User"
        git config user.email "<EMAIL>"
        
        # 创建大量文件
        for i in {1..100}; do
          echo "// File $i content" > "file$i.js"
          echo "// [[GBAI START]]" >> "file$i.js"
          echo "// AI generated code $i" >> "file$i.js"
          echo "// [[GBAI END]]" >> "file$i.js"
        done
        
        git add .
        git commit -m "Large commit with AI code"
        
    - name: Performance test
      run: |
        cd large-repo
        time node ../lib/ai-code-stats.js HEAD
        time node ../lib/cleanup-markers.js --quiet
