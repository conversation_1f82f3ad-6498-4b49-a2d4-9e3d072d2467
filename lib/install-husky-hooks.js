#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始安装 Husky Git Hooks...\n');

// 检查是否在Git仓库中
try {
  execSync('git rev-parse --git-dir', { stdio: 'ignore' });
} catch (error) {
  console.error('❌ 错误: 当前目录不是Git仓库');
  process.exit(1);
}

// 获取项目根目录
const projectRoot = execSync('git rev-parse --show-toplevel', { encoding: 'utf-8' }).trim();
const packageJsonPath = path.join(projectRoot, 'package.json');

// 检查package.json是否存在
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ 错误: 找不到package.json文件');
  process.exit(1);
}

// 读取package.json
let packageJson;
try {
  packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
} catch (error) {
  console.error('❌ 错误: 无法解析package.json文件', error);
  process.exit(1);
}

// 检查是否已安装husky
const hasHusky = packageJson.devDependencies && packageJson.devDependencies.husky;

if (!hasHusky) {
  console.log('❌ 当前工程没有安装 Husky， 请先安装');
  process.exit(1);
}

// 初始化husky
try {
  execSync('npx husky install', { stdio: 'inherit', cwd: projectRoot });
} catch (error) {
  console.error('❌ 初始化 Husky 失败:', error.message);
  process.exit(1);
}

// 创建.husky目录（如果不存在）
const huskyDir = path.join(projectRoot, '.husky');
if (!fs.existsSync(huskyDir)) {
  fs.mkdirSync(huskyDir, { recursive: true });
}

// 计算ai-code-stats脚本的路径
const aiCodeStatsPath = path.resolve(__dirname, 'ai-code-stats.js');

// 创建pre-commit hook
const preCommitPath = path.join(huskyDir, 'pre-commit');

// 检查是否已存在 pre-commit hook
let existingContent = '';
if (fs.existsSync(preCommitPath)) {
  existingContent = fs.readFileSync(preCommitPath, 'utf-8');

  // 检查是否已包含 AI 代码统计
  if (existingContent.includes('ai-code-stats.js')) {
    console.log('✅ Pre-commit hook 已包含 AI 代码统计，跳过安装');
    process.exit(0);
  }

  console.log('⚠️  检测到现有的 pre-commit hook，将进行合并');
}

// 构建新的 hook 内容
const aiStatsHook = `
# AI代码统计和清理 - Pre-commit Hook
echo "🔍 正在统计AI代码并清理标记..."

# 检查是否有staged文件
if ! git diff --cached --quiet; then
  # 执行AI代码统计和清理
  node "${aiCodeStatsPath}" --mode=pre-commit --detailed
  
  if [ $? -ne 0 ]; then
    echo "❌ AI代码统计失败，请检查错误信息"
    exit 1
  fi
fi
`;

let finalContent;
if (existingContent) {
  // 在现有内容后添加 AI 统计
  finalContent = existingContent.trimEnd() + '\n' + aiStatsHook;
} else {
  // 创建新的 hook
  finalContent = `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"
${aiStatsHook}`;
}

fs.writeFileSync(preCommitPath, finalContent);
fs.chmodSync(preCommitPath, '755');

console.log(`✅ Pre-commit hook 已创建: ${preCommitPath}`);

// 更新package.json的scripts（如果需要）
if (!packageJson.scripts) {
  packageJson.scripts = {};
}

if (!packageJson.scripts.prepare) {
  packageJson.scripts.prepare = 'husky install';
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ 已添加 prepare script 到 package.json');
}

console.log('\n🎉 Husky Git Hooks 安装完成！');
console.log('\n📋 配置说明:');
console.log('- Pre-commit hook: 在每次commit前自动统计AI代码并清理标记');
console.log('- 清理后的代码会自动重新staged，确保commit的代码是干净的');
console.log('- 如果没有AI标记，hook会快速通过不影响commit速度');

console.log('\n🔧 使用方法:');
console.log('0. 确保为你的 AI coding 工具添加了系统提示词');
console.log('1. 正常开发， AI工具会在代码中添加 [[GBAI START]] 和 [[GBAI END]] 标记');
console.log('2. 使用 git add 添加文件');
console.log('3. 使用 git commit 提交 - hook会自动处理AI代码统计和清理');
console.log('4. 提交的代码将不包含AI标记，但统计信息会显示在控制台');

console.log('\n💡 提示:');
console.log('- 如果需要跳过hook，可以使用: git commit --no-verify');
console.log('- 如果需要手动清理标记，可以使用: cleanup-ai-markers');
console.log('- 配置文件位置: .husky/pre-commit');