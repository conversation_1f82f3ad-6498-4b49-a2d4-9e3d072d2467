#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

/**
 * 清理工作目录中的AI标记
 * @param {boolean} verbose - 是否显示详细输出
 * @param {string[]} filesToClean - 要清理的文件列表，如果不提供则扫描所有文件
 */
function cleanupAIMarkers(verbose = true, filesToClean = null) {
  try {
    if (verbose) {
      console.log('开始清理AI标记...\n');
    }

    let allFiles;
    if (filesToClean && filesToClean.length > 0) {
      // 使用提供的文件列表
      allFiles = filesToClean.filter(file => {
        // 过滤掉不存在的文件和目录
        try {
          const fs = require('fs');
          return fs.statSync(file).isFile();
        } catch (error) {
          if (verbose) {
            console.log(`⚠ 跳过不存在的文件: ${file}`);
          }
          return false;
        }
      });

      if (verbose) {
        console.log(`📋 处理指定的 ${allFiles.length} 个文件`);
      }
    } else {
      // 获取当前工作目录中的所有文件（排除.git目录）
      const findCommand = `find . -type f -not -path "./.git/*" -not -path "./node_modules/*" -not -name "*.log" -not -name "*.tmp"`;
      allFiles = execSync(findCommand, { encoding: 'utf-8' })
        .trim()
        .split('\n')
        .filter(Boolean);

      if (verbose) {
        console.log(`📂 扫描工作目录，找到 ${allFiles.length} 个文件`);
      }
    }
    
    let cleanedFiles = 0;
    let totalMarkersRemoved = 0;
    const processedFiles = [];
    
    allFiles.forEach(filePath => {
      try {
        // 跳过清理脚本自身，避免破坏文件结构
        if (filePath.includes('cleanup-markers.js') || filePath.endsWith('.md')) {
          return;
        }
        
        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf-8');
        
        // 检查是否包含AI标记
        if (content.includes('[[GBAI START]]') || content.includes('[[GBAI END]]')) {
          // 清理AI标记
          let cleanedContent = content;
          let markersInFile = 0;
          
          // 计算并移除标记
          const starMatches = content.match(/.*\[\[GBAI START\]\].*/g) || [];
          const endMatches = content.match(/.*\[\[GBAI END\]\].*/g) || [];
          markersInFile = starMatches.length + endMatches.length;
          
          // 移除包含标记的整行
          cleanedContent = cleanedContent
            .split('\n')
            .filter(line => !line.includes('[[GBAI START]]') && !line.includes('[[GBAI END]]'))
            .join('\n');
          
          // 写回文件
          fs.writeFileSync(filePath, cleanedContent, 'utf-8');
          
          if (markersInFile > 0) {
            cleanedFiles++;
            totalMarkersRemoved += markersInFile;
            processedFiles.push({ file: filePath, markers: markersInFile });
            
            if (verbose) {
              console.log(`✓ ${filePath} (移除 ${markersInFile} 个标记)`);
            }
          }
        }
      } catch (error) {
        // 忽略无法处理的文件（如二进制文件）
        if (verbose && !error.message.includes('EISDIR') && !error.message.includes('ENOENT')) {
          console.error(`⚠ 跳过文件 ${filePath}: ${error.message}`);
        }
      }
    });
    
    // 输出汇总信息
    if (verbose) {
      console.log('\n===== 清理完成 =====');
      if (totalMarkersRemoved > 0) {
        console.log(`✅ 成功清理了 ${cleanedFiles} 个文件中的 ${totalMarkersRemoved} 个AI标记`);
        
        if (processedFiles.length > 0) {
          console.log('\n处理的文件列表:');
          processedFiles.forEach(({ file, markers }) => {
            console.log(`  - ${file}: ${markers} 个标记`);
          });
        }
        
        console.log('\n💡 提示: 现在可以安全地提交代码，不会包含AI标记');
      } else {
        console.log('ℹ️  没有发现需要清理的AI标记');
      }
    } else {
      // 静默模式下的简洁输出
      if (totalMarkersRemoved > 0) {
        console.log(`清理了 ${cleanedFiles} 个文件中的 ${totalMarkersRemoved} 个AI标记`);
      }
    }
    
    return { cleanedFiles, totalMarkersRemoved, processedFiles };
  } catch (error) {
    console.error(`❌ 清理AI标记时出错: ${error.message}`);
    return { cleanedFiles: 0, totalMarkersRemoved: 0, processedFiles: [] };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const verbose = !process.argv.includes('--quiet');

  // 检查是否提供了文件列表
  let filesToClean = null;
  const filesArgIndex = process.argv.indexOf('--files');
  if (filesArgIndex !== -1 && filesArgIndex + 1 < process.argv.length) {
    // 从 --files 参数后获取文件列表（以逗号分隔）
    const filesArg = process.argv[filesArgIndex + 1];
    filesToClean = filesArg.split(',').map(f => f.trim()).filter(Boolean);

    if (verbose) {
      console.log(`📋 接收到文件列表: ${filesToClean.join(', ')}`);
    }
  }

  const result = cleanupAIMarkers(verbose, filesToClean);

  // 设置退出码：0表示成功，1表示出错
  if (result.cleanedFiles >= 0) {
    process.exit(0);
  } else {
    process.exit(1);
  }
}

module.exports = { cleanupAIMarkers };
