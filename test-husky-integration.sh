#!/bin/bash

# AI代码统计工具 - <PERSON><PERSON>集成测试脚本

set -e

echo "🧪 开始测试 AI代码统计工具的 Husky 集成..."

# 创建测试目录
TEST_DIR="test-ai-stats-$(date +%s)"
mkdir "$TEST_DIR"
cd "$TEST_DIR"

echo "📁 创建测试目录: $TEST_DIR"

# 初始化Git仓库
git init
git config user.name "Test User"
git config user.email "<EMAIL>"

echo "🔧 初始化Git仓库"

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "test-ai-stats",
  "version": "1.0.0",
  "description": "Test project for AI code stats",
  "scripts": {},
  "devDependencies": {}
}
EOF

echo "📦 创建package.json"

# 安装ai-code-stats工具（假设已经全局安装或在父目录）
echo "🔗 链接ai-code-stats工具..."

# 创建测试文件（包含AI标记）
cat > test-file.js << 'EOF'
// 这是一个测试文件
function normalFunction() {
    return "这是普通代码";
}

// [[GBAI START]]
function aiGeneratedFunction() {
    // 这是AI生成的代码
    const result = "Hello from AI";
    return result;
}
// [[GBAI END]]

// 更多普通代码
const config = {
    name: "test"
};

// [[GBAI START]]
// 另一个AI生成的块
function anotherAIFunction() {
    console.log("Another AI function");
    return true;
}
// [[GBAI END]]
EOF

echo "📝 创建包含AI标记的测试文件"

# 创建另一个测试文件
cat > test-file2.py << 'EOF'
# Python测试文件
def normal_function():
    return "normal code"

# [[GBAI START]]
def ai_generated_function():
    """这是AI生成的Python函数"""
    result = "AI generated"
    return result
# [[GBAI END]]
EOF

echo "📝 创建第二个测试文件"

# 安装Husky hooks
echo "🚀 安装Husky hooks..."
if command -v install-husky-hooks >/dev/null 2>&1; then
    install-husky-hooks
else
    echo "⚠️  install-husky-hooks命令不可用，请确保ai-code-stats已正确安装"
    echo "尝试使用相对路径..."
    if [ -f "../lib/install-husky-hooks.js" ]; then
        node ../lib/install-husky-hooks.js
    else
        echo "❌ 找不到install-husky-hooks.js文件"
        exit 1
    fi
fi

# 测试1: 提交包含AI标记的文件
echo ""
echo "🧪 测试1: 提交包含AI标记的文件"
git add test-file.js test-file2.py

echo "📊 提交前的文件内容（应包含AI标记）:"
echo "--- test-file.js ---"
grep -n "GBAI" test-file.js || echo "未找到GBAI标记"
echo "--- test-file2.py ---"
grep -n "GBAI" test-file2.py || echo "未找到GBAI标记"

echo ""
echo "🔄 执行commit（应触发pre-commit hook）..."
git commit -m "测试提交：包含AI生成的代码

这个提交包含了AI生成的代码块，用于测试自动清理功能。"

echo ""
echo "📊 提交后的文件内容（应该已清理AI标记）:"
echo "--- test-file.js ---"
if grep -n "GBAI" test-file.js; then
    echo "❌ 错误: 文件中仍然包含GBAI标记"
    exit 1
else
    echo "✅ 成功: AI标记已被清理"
fi

echo "--- test-file2.py ---"
if grep -n "GBAI" test-file2.py; then
    echo "❌ 错误: 文件中仍然包含GBAI标记"
    exit 1
else
    echo "✅ 成功: AI标记已被清理"
fi

# 测试2: 提交不包含AI标记的文件
echo ""
echo "🧪 测试2: 提交不包含AI标记的文件"
cat > normal-file.js << 'EOF'
// 普通文件，不包含AI标记
function normalFunction() {
    return "这是普通代码";
}

const config = {
    name: "normal"
};
EOF

git add normal-file.js
echo "🔄 执行commit（应快速通过）..."
git commit -m "测试提交：普通代码文件"

# 测试3: 检查Git历史
echo ""
echo "🧪 测试3: 检查Git历史"
echo "📜 Git提交历史:"
git log --oneline

echo ""
echo "📊 最新提交的文件内容:"
git show --name-only HEAD

# 清理测试目录
cd ..
echo ""
echo "🧹 清理测试目录..."
rm -rf "$TEST_DIR"

echo ""
echo "🎉 所有测试通过！Husky集成工作正常。"
echo ""
echo "📋 测试总结:"
echo "✅ Husky hooks安装成功"
echo "✅ Pre-commit hook正确触发"
echo "✅ AI标记统计功能正常"
echo "✅ AI标记清理功能正常"
echo "✅ 清理后的代码正确提交"
echo "✅ 普通文件快速通过"
echo ""
echo "💡 现在可以在实际项目中使用 install-husky-hooks 命令来安装hooks"
